import { router } from 'expo-router';
import React, { useState } from 'react';
import { Modal, SafeAreaView, ScrollView, View } from 'react-native';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Text } from '@/components/ui/text';
import { useTranslation } from '@/hooks/useTranslation';
import { ConfigType } from '@/lib/types';

export default function AddConfigScreen() {
  const { t } = useTranslation();
  const [selectedType, setSelectedType] = useState<ConfigType | null>(null);
  const [showConfigForm, setShowConfigForm] = useState(false);

  const configTypes: { type: ConfigType; title: string; description: string }[] = [
    {
      type: 's-ui',
      title: t('configTypes.s-ui'),
      description: 'Sing-box UI 管理面板',
    },
    {
      type: 'x-ui',
      title: t('configTypes.x-ui'),
      description: 'X-UI 管理面板',
    },
    {
      type: '3x-ui',
      title: t('configTypes.3x-ui'),
      description: '3X-UI 管理面板',
    },
  ];

  const handleTypeSelect = (type: ConfigType) => {
    setSelectedType(type);
    setShowConfigForm(true);
  };

  const handleCloseForm = () => {
    setShowConfigForm(false);
    setSelectedType(null);
  };

  const handleFormSubmit = () => {
    // 表单提交后关闭模态框并返回主页
    setShowConfigForm(false);
    setSelectedType(null);
    router.back();
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <View style={styles.header}>
          <Text className="text-lg text-muted-foreground text-center">
            {t('addConfig.selectType')}
          </Text>
        </View>

        <View style={styles.typeList}>
          {configTypes.map((config) => (
            <Card key={config.type} style={styles.typeCard}>
              <View style={styles.cardContent}>
                <Text className="text-xl font-semibold">{config.title}</Text>
                <Text className="text-sm text-muted-foreground mt-2">
                  {config.description}
                </Text>
                <Button
                  variant="default"
                  onPress={() => handleTypeSelect(config.type)}
                  style={styles.selectButton}
                >
                  <Text>{t('common.add')}</Text>
                </Button>
              </View>
            </Card>
          ))}
        </View>
      </ScrollView>

      {/* 配置表单模态框 */}
      <Modal
        visible={showConfigForm}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={handleCloseForm}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Button
              variant="ghost"
              onPress={handleCloseForm}
            >
              <Text>{t('common.cancel')}</Text>
            </Button>
            <Text className="text-lg font-semibold">
              {selectedType && t('addConfig.title')} - {selectedType?.toUpperCase()}
            </Text>
            <View style={{ width: 60 }} />
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.formPlaceholder}>
              <Text className="text-center text-muted-foreground">
                配置表单 - 待实现
              </Text>
              <Text className="text-center text-sm text-muted-foreground mt-2">
                类型: {selectedType}
              </Text>
              <Button
                variant="default"
                onPress={handleFormSubmit}
                style={styles.submitButton}
              >
                <Text>{t('addConfig.submit')}</Text>
              </Button>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  typeList: {
    gap: 16,
  },
  typeCard: {
    padding: 0,
  },
  cardContent: {
    padding: 20,
  },
  selectButton: {
    marginTop: 16,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e5e5',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  formPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  submitButton: {
    marginTop: 24,
  },
});
